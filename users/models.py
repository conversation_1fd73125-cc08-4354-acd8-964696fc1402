import re

from django.conf import settings
from django.contrib.auth.models import AbstractBase<PERSON><PERSON>, BaseUserManager
from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON><PERSON>, J<PERSON><PERSON>ield
from django.core.mail import send_mail
from django.core.validators import RegexValidator
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from contacts.models import Contact

USERNAME_RE = re.compile(r'^[-\w]+$')

validate_canonical_username = RegexValidator(
    USERNAME_RE, _('Usernames may only contain letters, numbers, underscores, and hyphens.'))


class UserManager(BaseUserManager):
    def create_user(self, username=None, password=None, is_superuser=False,
                    **extra_fields):
        now = timezone.now()

        if not username:
            raise ValueError('{} must be provided' + settings.USERNAME_FIELD)

        email = extra_fields.pop("email", None) or None  # force None for empty value

        if settings.USERNAME_FIELD == 'email' and email is None:
            email = username

        user = self.model(username=username, email=email, is_active=True,
                          is_superuser=is_superuser, last_login=now,
                          **extra_fields)
        user.set_password(password)
        user.save(using=self._db)

        return user

    def create_superuser(self, email, password=None, **user_data):
        user_data["email"] = email
        return self.create_user(
            username=email,
            password=password,
            is_superuser=True,
            is_staff=True,
            **user_data,
        )


class User(AbstractBaseUser):
    USERNAME_FIELD = settings.USERNAME_FIELD
    username = models.CharField(_('Username'), max_length=255, unique=True)
    canonical_username = models.CharField(
        max_length=255, unique=True, null=True, blank=True,
        validators=[validate_canonical_username])
    email = CIEmailField(_('Email'), unique=True, null=True, blank=True)
    contact = models.OneToOneField(
        Contact, related_name='user', null=True, blank=True, on_delete=models.CASCADE)
    date_created = models.DateTimeField(default=timezone.now, editable=False)
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False, editable=True)
    is_superuser = models.BooleanField(default=False, editable=False)
    extra_data = JSONField(default=dict)
    objects = UserManager()

    def __str__(self):
        from users.utils import is_student
        name = self.get_full_name()
        ident = self.email
        if not ident and is_student(self):
            ident = f'{self.student.student_id}@{settings.STUDENT_USER_EMAIL_DOMAIN}'
        if name and ident:
            return f'{name} <{ident}>'
        elif name:
            return name
        else:
            return self.username

    @property
    def email_with_name(self):
        name = self.get_full_name()
        if name:
            return f'{name} <{self.email}>'

        return self.email

    @property
    def photo(self):
        if self.contact_id and self.contact.photo:
            return self.contact.photo

    def save(self, *args, **kwargs):
        if self.USERNAME_FIELD == 'email':
            self.username = self.email

        if self.USERNAME_FIELD == 'username':
            self.canonical_username = self.username

        if self.contact_id and self.email != self.contact.email:
            self.contact.email = self.email
            self.contact.save()

        super().save(*args, **kwargs)

    def get_first_name(self, default_attr=None):
        from .utils import is_student
        if is_student(self):
            return self.student.first_name.strip()

        if self.contact_id:
            return self.contact.first_name.strip()

        if default_attr and getattr(self, default_attr, None):
            return getattr(self, default_attr)

        return ''

    def get_last_name(self, default_attr=None):
        from .utils import is_student
        if is_student(self):
            return self.student.last_name.strip()

        if self.contact_id:
            return self.contact.last_name.strip()

        if default_attr and getattr(self, default_attr, None):
            return getattr(self, default_attr)

        return ''

    def get_full_name(self, default_attr=None):
        from .utils import is_student
        if is_student(self):
            return f'{self.student.first_name} {self.student.last_name}'.strip()

        if self.contact_id:
            return f'{self.contact.first_name} {self.contact.last_name}'.strip()

        if default_attr and getattr(self, default_attr, None):
            return getattr(self, default_attr)

        return ''

    def get_short_name(self):
        if not self.contact_id:
            return self.email
        return self.contact.first_name

    def email_user(self, subject, message, from_email=None, **kwargs):
        send_mail(subject, message, from_email, [self.email], **kwargs)

    def has_perm(self, perm, obj=None):
        if self.is_superuser:
            return True
        app_label, perm = perm.split('.')
        return self.has_module_perms(app_label)

    def has_module_perms(self, app_label):
        return self.is_superuser
