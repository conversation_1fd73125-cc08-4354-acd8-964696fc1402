version: '3.2'

# TODO: Merge keys are deprecated in YAML 1.2. Find an alternative system 
# for generating docker-compose configs in an easily maintainable way.

# Base config for hive service
x-hive-service: &hive-service
  image: hive:dev
  profiles: ["dev"]
  depends_on:
    - nginx
    - hive-db
  env_file: .env
  stdin_open: true
  tty: true
  networks:
    boldidea:
      aliases:
        - hive.${BASE_DOMAIN}
  volumes:
    - ..:/src
    - hive-media:/hive/media
    - hive-static:/hive/static
    - ${HOME}/.ipython:/home/<USER>/.ipython
 
# Base config for postgres
x-pg-defaults: &pg-defaults
  image: postgis/postgis:14-3.3-alpine
  volumes:
    - postgres-data:/var/lib/postgresql/data
  environment:
    - POSTGRES_USER=${DJANGO_DB_USER}
    - POSTGRES_DB=${DJANGO_DB_NAME}
    - POSTGRES_PASSWORD=${<PERSON><PERSON><PERSON><PERSON>_DB_PASSWORD}
    - PGPASSWORD=${DJAN<PERSON><PERSON>_DB_PASSWORD}
  networks:
    - boldidea

# Base config for postgres commands
x-pg-command: &pg-command
  <<: *pg-defaults
  profiles: ["commands", "dev"]
  depends_on:
    - hive-db

services:
  # Gateway for all hive apps
  nginx:
    profiles: ["dev"]
    container_name: hive-nginx
    image: nginx:alpine
    ports:
      - 8000:8000
    environment:
      - BASE_DOMAIN
    volumes:
      - ssl-certs:/hive/ssl
      - ./configs/dev/nginx:/etc/nginx/templates
    networks:
      boldidea:
        aliases:
          - hive.${BASE_DOMAIN}
          - my.${BASE_DOMAIN}
          - clubs.${BASE_DOMAIN}
          - api.${BASE_DOMAIN}
          - sso.${BASE_DOMAIN}

  # Postgres database
  hive-db:
    profiles: ["dev"]
    container_name: hive-db
    <<: *pg-defaults

  # Hive apps
  hive:
    <<: *hive-service
    container_name: hive
    environment:
      - HIVE_SITE=hive
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: dev
      args:
        HIVE_ENVIRONMENT: production
  student:
    <<: *hive-service
    container_name: hive-student
    environment:
      - HIVE_SITE=student
  clubs:
    <<: *hive-service
    container_name: hive-clubs
    environment:
      - HIVE_SITE=clubs
  api:
    <<: *hive-service
    container_name: hive-api
    environment:
      - HIVE_SITE=api
  sso:
    <<: *hive-service
    container_name: hive-sso
    environment:
      - HIVE_SITE=sso

  #
  # Production builds
  #
  hive-prod:
    profiles: ["production"]
    image: boldidea.azurecr.io/hive
    environment:
      - HIVE_SITE=hive
    build:
      cache_from:
        - boldidea.azurecr.io/hive
      dockerfile: docker/Dockerfile
      target: prod
      context: ..
      args:
        HIVE_ENVIRONMENT: production
        WSGI_APP: azure_support.wsgi:application
        WSGI_PORT: 8000
      platforms:
        - linux/amd64

  nginx-prod:
    profiles: ["production"]
    image: boldidea.azurecr.io/hive-nginx
    build:
      cache_from:
        - boldidea.azurecr.io/hive-nginx
      dockerfile: docker/Dockerfile
      target: nginx-prod
      context: ..
      args:
        HIVE_ENVIRONMENT: production
      platforms:
        - linux/amd64

  #
  # Utility commands
  #
  
  # Generates a .env file for development
  mkenv:
    profiles: ["commands"]
    image: python
    entrypoint: python /usr/local/bin/mkenv.py
    volumes:
      - ./utils/mkenv.py:/usr/local/bin/mkenv.py
      - ./env.template:/hive/env.template

  # Generates local SSL certs
  localcerts:
    profiles: ["commands"]
    image: python
    environment:
      - BASE_DOMAIN
      - LOCALCERTS_SSL_DIR=/hive/ssl
    entrypoint: python /usr/local/bin/localcerts.py
    command: -h
    volumes:
      - ./utils/localcerts.py:/usr/local/bin/localcerts.py
      - ssl-certs:/hive/ssl

  poetry:
    profiles: ["commands"]
    entrypoint: /opt/poetry/bin/poetry
    working_dir: /build
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: build
    container_name: hive-poetry
    volumes:
      - ..:/build
      - poetry-cache:/opt/poetry/cache

  poetry-lock:
    profiles: ["commands"]
    command: /opt/poetry/bin/poetry lock --no-update
    working_dir: /build
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: build-base
    container_name: hive-poetry-lock
    volumes:
      - ..:/build
      - poetry-cache:/opt/poetry/cache

  manage:
    profiles: ["commands", "dev"]
    environment:
      - HIVE_SITE
    depends_on:
      - hive-db
    entrypoint: manage
    command: help
    image: hive:dev
    container_name: hive-manage
    env_file: .env
    stdin_open: true
    tty: true
    volumes:
      - ..:/src
      - hive-media:/hive/media
      - hive-static:/hive/static
      - hive-tmp:/hive/tmp
      - db-backups:/hive/db-backups
      - ${HOME}/.ipython:/home/<USER>/.ipython
    networks:
      - boldidea

  # Easy psql shell
  # NOTE: when piping sql to this command, use `docker-compose run -T psql`
  psql:
    <<: *pg-command
    container_name: hive_psql
    entrypoint: psql -h "hive-db" -p "5432" -U ${DJANGO_DB_USER} -w

  # Easily restore from a database dump, eg:
  #     cat /tmp/foo.db | docker-compose run -T pg_restore
  pg_restore:
    <<: *pg-command
    entrypoint: pg_restore -h "hive-db" -p "5432" -U ${DJANGO_DB_USER} -w
    command: --no-owner --dbname=${DJANGO_DB_NAME}

  pg_dump:
    <<: *pg-command
    entrypoint: pg_dump -Fc -h "hive-db" -p "5432" -U ${DJANGO_DB_USER} -w
    command: --dbname=${DJANGO_DB_NAME}

volumes:
  db-backups:
  ssl-certs:
  hive-media:
  hive-static:
  hive-tmp:
  postgres-data:
  poetry-cache:

networks:
  boldidea:
    name: boldidea
